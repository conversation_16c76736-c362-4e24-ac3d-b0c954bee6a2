using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors.Repository;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Modules.Helpers.PermissionManagementForm
{
    /// <summary>
    /// Helper class for managing permission grids in permission management forms.
    /// Provides grid configuration, data binding, and event handling for permission editing.
    /// </summary>
    public class PermissionGridHelper
    {
        #region Grid Configuration

        /// <summary>
        /// Configure role permissions grid with checkbox editing
        /// </summary>
        /// <param name="gridControl">Grid control to configure</param>
        /// <param name="gridView">Grid view to configure</param>
        public void ConfigureRolePermissionsGrid(GridControl gridControl, GridView gridView)
        {
            try
            {
                // Clear existing columns and repository items
                gridView.Columns.Clear();
                gridControl.RepositoryItems.Clear();

                // Create checkbox repository item
                var checkBoxRepository = new RepositoryItemCheckEdit();
                checkBoxRepository.Name = "CheckBoxRepository";
                checkBoxRepository.ValueChecked = true;
                checkBoxRepository.ValueUnchecked = false;
                checkBoxRepository.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked;
                gridControl.RepositoryItems.Add(checkBoxRepository);

                // Configure grid view options
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ShowAutoFilterRow = true;
                gridView.OptionsBehavior.Editable = true;
                gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
                gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
                gridView.OptionsView.ColumnAutoWidth = false;

                // Add columns
                AddColumn(gridView, "FormName", "Form Name", 200, false);
                AddColumn(gridView, "DisplayName", "Display Name", 250, false);
                AddColumn(gridView, "Category", "Category", 120, false);
                AddCheckboxColumn(gridView, "ReadPermission", "Read", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "NewPermission", "New", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "EditPermission", "Edit", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "DeletePermission", "Delete", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "PrintPermission", "Print", 80, checkBoxRepository);

                // Configure appearance
                ConfigureGridAppearance(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error configuring role permissions grid: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Configure user permissions grid with role vs override color coding
        /// </summary>
        /// <param name="gridControl">Grid control to configure</param>
        /// <param name="gridView">Grid view to configure</param>
        public void ConfigureUserPermissionsGrid(GridControl gridControl, GridView gridView)
        {
            try
            {
                // Clear existing columns and repository items
                gridView.Columns.Clear();
                gridControl.RepositoryItems.Clear();

                // Create checkbox repository item
                var checkBoxRepository = new RepositoryItemCheckEdit();
                checkBoxRepository.Name = "CheckBoxRepository";
                checkBoxRepository.ValueChecked = true;
                checkBoxRepository.ValueUnchecked = false;
                checkBoxRepository.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked;
                gridControl.RepositoryItems.Add(checkBoxRepository);

                // Configure grid view options
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ShowAutoFilterRow = true;
                gridView.OptionsBehavior.Editable = true;
                gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
                gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
                gridView.OptionsView.ColumnAutoWidth = false;

                // Add columns
                AddColumn(gridView, "FormName", "Form Name", 180, false);
                AddColumn(gridView, "DisplayName", "Display Name", 200, false);
                AddColumn(gridView, "Category", "Category", 100, false);
                
                // Role permissions (read-only, for reference)
                AddColumn(gridView, "RoleRead", "Role Read", 80, false);
                AddColumn(gridView, "RoleNew", "Role New", 80, false);
                AddColumn(gridView, "RoleEdit", "Role Edit", 80, false);
                AddColumn(gridView, "RoleDelete", "Role Delete", 80, false);
                AddColumn(gridView, "RolePrint", "Role Print", 80, false);

                // User override permissions (editable)
                AddCheckboxColumn(gridView, "UserRead", "User Read", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "UserNew", "User New", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "UserEdit", "User Edit", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "UserDelete", "User Delete", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "UserPrint", "User Print", 80, checkBoxRepository);

                // Effective permissions (calculated, read-only)
                AddColumn(gridView, "EffectiveRead", "Effective Read", 90, false);
                AddColumn(gridView, "EffectiveNew", "Effective New", 90, false);
                AddColumn(gridView, "EffectiveEdit", "Effective Edit", 90, false);
                AddColumn(gridView, "EffectiveDelete", "Effective Delete", 90, false);
                AddColumn(gridView, "EffectivePrint", "Effective Print", 90, false);

                // Configure appearance with color coding
                ConfigureUserPermissionsAppearance(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error configuring user permissions grid: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Data Binding

        /// <summary>
        /// Load role permissions data into grid
        /// </summary>
        /// <param name="gridControl">Grid control to load data into</param>
        /// <param name="roleId">Role ID to load permissions for</param>
        public void LoadRolePermissions(GridControl gridControl, int roleId)
        {
            try
            {
                var permissions = GetRolePermissionsForGrid(roleId);
                gridControl.DataSource = permissions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Load user permissions data into grid with role comparison
        /// </summary>
        /// <param name="gridControl">Grid control to load data into</param>
        /// <param name="userId">User ID to load permissions for</param>
        public void LoadUserPermissions(GridControl gridControl, int userId)
        {
            try
            {
                var permissions = GetUserPermissionsForGrid(userId);
                gridControl.DataSource = permissions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Add a regular column to the grid view
        /// </summary>
        private void AddColumn(GridView gridView, string fieldName, string caption, int width, bool allowEdit)
        {
            var column = gridView.Columns.AddField(fieldName);
            column.Caption = caption;
            column.Width = width;
            column.OptionsColumn.AllowEdit = allowEdit;
            column.OptionsColumn.AllowFocus = allowEdit;
            column.Visible = true;
        }

        /// <summary>
        /// Add a checkbox column to the grid view
        /// </summary>
        private void AddCheckboxColumn(GridView gridView, string fieldName, string caption, int width, 
            RepositoryItemCheckEdit checkBoxRepository)
        {
            var column = gridView.Columns.AddField(fieldName);
            column.Caption = caption;
            column.Width = width;
            column.ColumnEdit = checkBoxRepository;
            column.OptionsColumn.AllowEdit = true;
            column.OptionsColumn.AllowFocus = true;
            column.Visible = true;
        }

        /// <summary>
        /// Configure basic grid appearance
        /// </summary>
        private void ConfigureGridAppearance(GridView gridView)
        {
            // Set alternating row colors
            gridView.OptionsView.EnableAppearanceEvenRow = true;
            gridView.Appearance.EvenRow.BackColor = Color.FromArgb(245, 245, 245);
            
            // Set header appearance
            gridView.Appearance.HeaderPanel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            gridView.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            
            // Set row height
            gridView.RowHeight = 25;
        }

        /// <summary>
        /// Configure user permissions grid appearance with color coding
        /// </summary>
        private void ConfigureUserPermissionsAppearance(GridView gridView)
        {
            ConfigureGridAppearance(gridView);
            
            // Add custom row cell style event for color coding
            gridView.RowCellStyle += (sender, e) =>
            {
                if (e.Column.FieldName.StartsWith("Role"))
                {
                    e.Appearance.BackColor = Color.FromArgb(230, 230, 250); // Light blue for role permissions
                }
                else if (e.Column.FieldName.StartsWith("User"))
                {
                    e.Appearance.BackColor = Color.FromArgb(255, 250, 230); // Light yellow for user overrides
                }
                else if (e.Column.FieldName.StartsWith("Effective"))
                {
                    e.Appearance.BackColor = Color.FromArgb(230, 255, 230); // Light green for effective permissions
                    e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Bold);
                }
            };
        }

        /// <summary>
        /// Get role permissions formatted for grid display
        /// </summary>
        private DataTable GetRolePermissionsForGrid(int roleId)
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("FormName", typeof(string));
            dataTable.Columns.Add("DisplayName", typeof(string));
            dataTable.Columns.Add("Category", typeof(string));
            dataTable.Columns.Add("ReadPermission", typeof(bool));
            dataTable.Columns.Add("NewPermission", typeof(bool));
            dataTable.Columns.Add("EditPermission", typeof(bool));
            dataTable.Columns.Add("DeletePermission", typeof(bool));
            dataTable.Columns.Add("PrintPermission", typeof(bool));

            // Get all forms and their permissions for this role
            var forms = FormsConfigurationService.GetAllForms();
            var rolePermissions = PermissionService.GetRolePermissions(roleId);

            foreach (var form in forms)
            {
                var permission = rolePermissions.FirstOrDefault(p => p.FormName == form.FormName);
                var row = dataTable.NewRow();
                row["FormName"] = form.FormName;
                row["DisplayName"] = form.DisplayName;
                row["Category"] = form.Category;
                row["ReadPermission"] = permission?.ReadPermission ?? false;
                row["NewPermission"] = permission?.NewPermission ?? false;
                row["EditPermission"] = permission?.EditPermission ?? false;
                row["DeletePermission"] = permission?.DeletePermission ?? false;
                row["PrintPermission"] = permission?.PrintPermission ?? false;
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        /// <summary>
        /// Get user permissions formatted for grid display with role comparison
        /// </summary>
        private DataTable GetUserPermissionsForGrid(int userId)
        {
            var dataTable = new DataTable();
            // Form info columns
            dataTable.Columns.Add("FormName", typeof(string));
            dataTable.Columns.Add("DisplayName", typeof(string));
            dataTable.Columns.Add("Category", typeof(string));
            
            // Role permissions (read-only reference)
            dataTable.Columns.Add("RoleRead", typeof(bool));
            dataTable.Columns.Add("RoleNew", typeof(bool));
            dataTable.Columns.Add("RoleEdit", typeof(bool));
            dataTable.Columns.Add("RoleDelete", typeof(bool));
            dataTable.Columns.Add("RolePrint", typeof(bool));
            
            // User override permissions (editable)
            dataTable.Columns.Add("UserRead", typeof(bool?));
            dataTable.Columns.Add("UserNew", typeof(bool?));
            dataTable.Columns.Add("UserEdit", typeof(bool?));
            dataTable.Columns.Add("UserDelete", typeof(bool?));
            dataTable.Columns.Add("UserPrint", typeof(bool?));
            
            // Effective permissions (calculated)
            dataTable.Columns.Add("EffectiveRead", typeof(bool));
            dataTable.Columns.Add("EffectiveNew", typeof(bool));
            dataTable.Columns.Add("EffectiveEdit", typeof(bool));
            dataTable.Columns.Add("EffectiveDelete", typeof(bool));
            dataTable.Columns.Add("EffectivePrint", typeof(bool));

            // Get user's effective permissions
            var userPermissions = PermissionService.GetUserEffectivePermissions(userId);
            var forms = FormsConfigurationService.GetAllForms();

            foreach (var form in forms)
            {
                var permission = userPermissions.FirstOrDefault(p => p.FormName == form.FormName);
                var row = dataTable.NewRow();
                row["FormName"] = form.FormName;
                row["DisplayName"] = form.DisplayName;
                row["Category"] = form.Category;
                
                if (permission != null)
                {
                    // Role permissions (from role)
                    row["RoleRead"] = permission.RoleReadPermission;
                    row["RoleNew"] = permission.RoleNewPermission;
                    row["RoleEdit"] = permission.RoleEditPermission;
                    row["RoleDelete"] = permission.RoleDeletePermission;
                    row["RolePrint"] = permission.RolePrintPermission;
                    
                    // User overrides (null means inherit from role)
                    row["UserRead"] = permission.UserReadPermission;
                    row["UserNew"] = permission.UserNewPermission;
                    row["UserEdit"] = permission.UserEditPermission;
                    row["UserDelete"] = permission.UserDeletePermission;
                    row["UserPrint"] = permission.UserPrintPermission;
                    
                    // Effective permissions (final result)
                    row["EffectiveRead"] = permission.ReadPermission;
                    row["EffectiveNew"] = permission.NewPermission;
                    row["EffectiveEdit"] = permission.EditPermission;
                    row["EffectiveDelete"] = permission.DeletePermission;
                    row["EffectivePrint"] = permission.PrintPermission;
                }
                else
                {
                    // No permissions found - set all to false
                    row["RoleRead"] = false;
                    row["RoleNew"] = false;
                    row["RoleEdit"] = false;
                    row["RoleDelete"] = false;
                    row["RolePrint"] = false;
                    row["UserRead"] = DBNull.Value;
                    row["UserNew"] = DBNull.Value;
                    row["UserEdit"] = DBNull.Value;
                    row["UserDelete"] = DBNull.Value;
                    row["UserPrint"] = DBNull.Value;
                    row["EffectiveRead"] = false;
                    row["EffectiveNew"] = false;
                    row["EffectiveEdit"] = false;
                    row["EffectiveDelete"] = false;
                    row["EffectivePrint"] = false;
                }
                
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        #endregion
    }
}
