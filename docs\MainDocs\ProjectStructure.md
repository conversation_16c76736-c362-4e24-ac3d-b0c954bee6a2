# ProManage Project Structure

This document provides a comprehensive overview of the ProManage project's folder hierarchy, organization principles, and file placement guidelines.

## Root Directory Structure

```
ProManage/
├── Forms/                          # Windows Forms UI components (organized by type)
│   ├── MainForms/                  # Primary business forms accessible via ribbon
│   ├── ChildForms/                 # Secondary forms called by MainForms
│   ├── CommonForms/                # Basic shared infrastructure forms
│   ├── ReusableForms/              # Utility forms and user controls
│   ├── Dialogs/                    # Modal dialog forms
│   └── EntryForms/                 # Data entry forms (future expansion)
├── Modules/                        # Core business logic and data access
│   ├── Connections/                # Database connectivity and transaction management
│   ├── Data/                       # Form-specific repositories and data access
│   ├── Models/                     # Form-specific data models and entities
│   ├── Helpers/                    # Form-specific helpers and shared utilities
│   ├── Reports/                    # Form-specific report generation logic
│   ├── Procedures/                 # SQL query files organized by module
│   ├── Services/                   # Application services and business logic
│   ├── UI/                         # UI-specific services and utilities
│   ├── Licensing/                  # License management and validation
│   └── Config/                     # Configuration files and settings
├── Resources/                      # Application resources (images, icons, SVG files)
├── ProManage.Services/            # Service layer project (separate assembly)
├── docs/                          # Project documentation
│   ├── MainDocs/                   # Core documentation files
│   └── Users and Role management.md # RBAC system documentation
├── Tasks/                         # Project task tracking and management
├── Tests/                         # Unit tests and test files
├── TestResults/                   # Test execution results and artifacts
├── Waste Docs/                    # Legacy/backup documentation files
├── References/                    # External library references
│   ├── DevExpress/                 # DevExpress component references
│   ├── Syncfusion/                 # Syncfusion component references
│   ├── NuGet/                      # NuGet package references
│   └── System/                     # System assembly references
├── packages/                      # NuGet package cache and dependencies
├── bin/                          # Build output directory
├── obj/                          # Build intermediate files
├── lib/                          # Local library dependencies
├── Properties/                    # Assembly properties and application settings
├── ProManage.csproj              # Main project file
├── ProManage.sln                 # Visual Studio solution file
├── App.config                    # Application configuration
├── Development.config            # Development environment configuration
├── Program.cs                    # Application entry point
├── TypeResolver.cs               # Type resolution utilities
└── app.manifest                  # Application manifest file
```

## Modules Folder Organization

The `Modules/` folder contains the core business logic organized by functional area:

### Modules/Connections/
**Purpose**: Centralized database connectivity and transaction management
- `DatabaseConnectionManager.cs` - Singleton connection manager
- `DatabaseTransactionService.cs` - Transaction handling service
- `DatabaseQueryExecutor.cs` - Query execution utilities

### Modules/Data/
**Purpose**: Data access layer organized by form-specific folders
```
Modules/Data/
├── EstimateForm/
│   ├── EstimateForm-Repository.cs     # Estimate data operations
│   └── EstimateForm-QueryService.cs   # Estimate query execution
├── LoginForm/
│   └── LoginForm-UserManager.cs       # User authentication and session management
├── PermissionManagementForm/
│   ├── PermissionForm-Repository.cs   # Permission data operations
│   └── RoleForm-Repository.cs         # Role data operations
└── SQLQueries.cs                      # SQL query constants and organization
```

### Modules/Models/
**Purpose**: Data models organized by form-specific folders
```
Modules/Models/
├── EstimateForm/
│   ├── EstimateForm-HeaderModel.cs    # Estimate header data model
│   └── EstimateForm-DetailModel.cs    # Estimate detail line items model
├── LoginForm/
│   └── LoginForm-UserModel.cs         # User entity model
├── PermissionManagementForm/
│   ├── PermissionModels.cs            # Permission-related models
│   ├── RoleModels.cs                  # Role-related models
│   └── UserPermissionModels.cs        # User permission models
└── UserMasterForm/
    └── UserMasterForm-Model.cs        # User master data model
```

### Modules/Helpers/
**Purpose**: Utility classes organized by form-specific folders and shared utilities
```
Modules/Helpers/
├── EstimateForm/
│   ├── EstimateForm-Helper.cs         # UI helpers (grid, navigation, form state)
│   └── EstimateForm-Validation.cs     # Data validation and business rules
├── PermissionManagementForm/
│   ├── PermissionForm-Helper.cs       # Permission UI helpers
│   ├── PermissionForm-Validation.cs   # Permission validation logic
│   └── RoleForm-Helper.cs             # Role management helpers
├── UserMasterForm/
│   ├── UserMasterForm-Helper.cs       # User form helpers
│   └── UserMasterForm-Validation.cs   # User validation logic
├── ConfigurationHelper.cs             # Shared: Application configuration
└── SQLQueryLoader.cs                  # Shared: SQL file loading utilities
```

### Modules/UI/
**Purpose**: UI-specific services and utilities
- `ProgressIndicatorService.cs` - Centralized progress indicator service for MainFrame status bar

### Modules/Licensing/
**Purpose**: License management and validation
- `LicenseManager.cs` - License validation and management
- `LicenseUsageMode.cs` - License usage mode enumeration

### Modules/Reports/
**Purpose**: Report generation and management
- `ReportManager.cs` - Report creation and display utilities

### Modules/Procedures/
**Purpose**: SQL query files organized by functional module

```
Modules/Procedures/
├── Estimate/                      # Estimate-related SQL queries
│   ├── EstimateCRUD.sql          # Create, Read, Update, Delete operations
│   ├── EstimateDelete.sql        # Delete operations
│   ├── EstimateListing.sql       # List and search operations
│   ├── EstimateNavigation.sql    # Navigation queries (first, last, next, prev)
│   ├── EstimateRetrieval.sql     # Retrieval operations
│   └── EstimateUtilities.sql     # Utility queries
├── SQLQuery/                      # SQL Query tool operations
│   ├── GetAllTables.sql          # Database schema queries
│   ├── GetTableColumns.sql
│   ├── GetTableConstraints.sql
│   ├── GetTableData.sql
│   ├── GetTableForeignKeys.sql
│   ├── GetTableIndexes.sql
│   └── GetTablePrimaryKey.sql
├── System/                        # System-level queries
│   ├── GetTableColumns.sql
│   └── GetTables.sql
├── User/                          # User management queries
│   ├── GetUser.sql
│   ├── SaveUser.sql
│   └── ValidateUser.sql
└── README.md                      # SQL procedures documentation
```

## Forms Directory Structure

The Forms folder is organized into specialized subfolders based on form types and their roles in the application architecture:

```
Forms/
├── MainForms/                   # Forms accessible via ribbon menu in MainFrame
│   ├── DatabaseForm.cs          # Database configuration form
│   ├── DatabaseForm.Designer.cs
│   ├── DatabaseForm.resx
│   ├── ParametersForm.cs        # Application parameter management
│   ├── ParametersForm.Designer.cs
│   ├── ParametersForm.resx
│   ├── PermissionManagementForm.cs  # Permission management interface
│   ├── PermissionManagementForm.Designer.cs
│   ├── PermissionManagementForm.resx
│   ├── RoleMasterForm.cs        # Role management form
│   ├── RoleMasterForm.Designer.cs
│   ├── RoleMasterForm.resx
│   ├── SQLQueryForm.cs          # SQL query execution tool
│   ├── SQLQueryForm.Designer.cs
│   ├── SQLQueryForm.resx
│   ├── UserManagementListForm.cs    # User listing and management
│   ├── UserManagementListForm.Designer.cs
│   ├── UserManagementListForm.resx
│   ├── UserMasterForm.cs        # User master data entry
│   ├── UserMasterForm.Designer.cs
│   └── UserMasterForm.resx
├── ChildForms/                  # Forms called by MainForms as child windows
│   ├── EstimateForm.cs          # Main estimate management form
│   ├── EstimateForm.Designer.cs
│   └── EstimateForm.resx
├── CommonForms/                 # Basic shared forms available to all users
│   ├── AboutBox.cs              # Application about dialog
│   ├── AboutBox.Designer.cs
│   ├── AboutBox.resx
│   ├── LoginForm.cs             # User authentication form
│   ├── LoginForm.Designer.cs
│   ├── LoginForm.resx
│   ├── MainFrame.cs             # Main MDI container window
│   ├── MainFrame.Designer.cs
│   └── MainFrame.resx
├── ReusableForms/               # Forms that can be called by any other form
│   ├── MenuRibbon.cs            # Reusable ribbon user control
│   ├── MenuRibbon.Designer.cs
│   ├── MenuRibbon.resx
│   ├── ParamEntryForm.cs        # Parameter entry dialog
│   ├── ParamEntryForm.Designer.cs
│   ├── ParamEntryForm.resx
│   ├── PrintPreviewForm.cs      # Centralized print preview form
│   ├── PrintPreviewForm.Designer.cs
│   └── PrintPreviewForm.resx
├── Dialogs/                     # Common dialog forms
│   ├── RoleCreateEditDialog.cs  # Role creation/editing dialog
│   ├── RoleCreateEditDialog.Designer.cs
│   └── RoleCreateEditDialog.resx
└── EntryForms/                  # Data entry forms (currently empty)
```

### Form Hierarchy and Calling Relationships

**1. MainFrame (CommonForms)** - The root MDI container
- Hosts all other forms as child windows
- Contains the main ribbon navigation
- Manages the application's overall UI state

**2. MainForms** - Primary business forms accessible via ribbon menu
- Opened as child forms within MainFrame
- Include major functional areas (Database, Parameters, Permissions, Users, SQL Query)
- Can call ChildForms and ReusableForms as needed

**3. ChildForms** - Secondary forms called by MainForms
- EstimateForm: Called from MainForms for estimate management
- Opened as child windows within the MDI container
- Focus on specific business processes

**4. ReusableForms** - Utility forms available to any other form
- MenuRibbon: User control for consistent ribbon interface across forms
- ParamEntryForm: Generic parameter entry dialog
- PrintPreviewForm: Centralized print preview and document viewer

**5. CommonForms** - Basic application infrastructure
- LoginForm: Authentication before MainFrame access
- AboutBox: Application information dialog
- MainFrame: The main application container



## Reports Directory Structure

ProManage implements a form-specific organization pattern for reports, with report generation logic and templates organized by the forms they serve:

### Modules/Reports/ - Report Generation Logic

```
Modules/Reports/
├── Estimate/                    # EstimateForm-related reports
│   ├── EstimateForm-PrintLayout.cs      # DevExpress report template
│   ├── EstimateForm-PrintLayout.Designer.cs
│   ├── EstimateForm-PrintLayout.resx
│   └── EstimateReportService.cs         # Report data service and generation logic
└── [FormName]/                  # Future form-specific report folders
    ├── [FormName]-PrintLayout.cs        # Report template for the form
    ├── [FormName]-PrintLayout.Designer.cs
    ├── [FormName]-PrintLayout.resx
    └── [FormName]ReportService.cs       # Report service for the form
```

### Report Organization Pattern

**Form-Specific Report Folders:**
- Each form that requires reporting gets its own subfolder in `Modules/Reports/`
- Folder name matches the form name (e.g., `Estimate/` for EstimateForm)
- Contains both report templates and report generation services

**Report Template Files:**
- `[FormName]-PrintLayout.cs` - DevExpress XtraReport template with layout design
- `[FormName]-PrintLayout.Designer.cs` - Auto-generated designer file
- `[FormName]-PrintLayout.resx` - Report resources and embedded content

**Report Service Files:**
- `[FormName]ReportService.cs` - Service class for data extraction and report population
- Handles data binding, formatting, and report generation logic
- Integrates with form data and database repositories

### Report Integration Architecture

**Data Flow:**
1. **Form Data** → **Report Service** → **Report Template** → **Print Preview**
2. Report services extract data from form repositories
3. Report templates receive populated data for rendering
4. Print preview displays reports in backstage view or dedicated forms

**Current Implementation:**
- **EstimateForm**: Complete reporting implementation with professional layout
- **PrintPreviewForm**: Centralized print preview form for all reports
- **Backstage Integration**: Reports embedded in form ribbon backstage views

**Future Expansion:**
- Additional forms will follow the same pattern: `Modules/Reports/[FormName]/`
- Each new form requiring reports gets its own dedicated folder
- Consistent naming and organization across all report implementations

## Dependencies and Relationships

### Data Flow Architecture
1. **Forms Layer** → **Modules/Data** → **Modules/Connections** → **Database**
2. **Forms** use **Modules/Models** for data binding
3. **Modules/Data** uses **Modules/Helpers** for SQL loading
4. **Modules/Reports** uses **Modules/Models** for report data

### Key Relationships
- **EstimateForm.cs** ↔ **EstimateForm-Repository.cs** ↔ **EstimateForm-HeaderModel.cs**
- **LoginForm.cs** ↔ **LoginForm-UserManager.cs** ↔ **LoginForm-UserModel.cs**
- **All Data classes** → **DatabaseConnectionManager.cs** for database access
- **SQL queries** loaded via **SQLQueryLoader.cs** from **Modules/Procedures/**

### Namespace Organization

**Forms Namespace Structure:**
- **MainForms**: `ProManage.Forms.MainForms`
- **ChildForms**: `ProManage.Forms.ChildForms`
- **CommonForms**: `ProManage.Forms.CommonForms`
- **ReusableForms**: `ProManage.Forms.ReusableForms`
- **Dialogs**: `ProManage.Forms.Dialogs`
- **EntryForms**: `ProManage.Forms.EntryForms`

**Modules Namespace Structure:**
- **Data**: `ProManage.Modules.Data.[FormName]`
- **Models**: `ProManage.Modules.Models.[FormName]`
- **Helpers**: `ProManage.Modules.Helpers.[FormName]`
- **Reports**: `ProManage.Modules.Reports.[FormName]`
- **Connections**: `ProManage.Modules.Connections`
- **Services**: `ProManage.Modules.Services`
- **UI**: `ProManage.Modules.UI`
- **Licensing**: `ProManage.Modules.Licensing`

**Other Namespaces:**
- **Services**: `ProManage.Services` (separate project)
- **Tests**: `ProManage.Tests`

## File Placement Guidelines

### **CRITICAL RULE: Form-Specific Folder Organization**

**All form-related files MUST be organized in folders named after their corresponding form.** This applies to:
- `Forms/[FormType]/` - Forms organized by type and calling hierarchy
- `Modules/Data/[FormName]/` - Form-specific repositories and data services
- `Modules/Models/[FormName]/` - Form-specific data models and entities
- `Modules/Helpers/[FormName]/` - Form-specific helpers and validation
- `Modules/Reports/[FormName]/` - Form-specific report templates and services

### Forms Folder Placement Rules

**Forms/MainForms/**
- Forms accessible via ribbon menu in MainFrame
- Primary business functionality forms
- Examples: DatabaseForm, ParametersForm, PermissionManagementForm, UserMasterForm

**Forms/ChildForms/**
- Forms called by MainForms as child windows
- Secondary business process forms
- Examples: EstimateForm (called from MainForms)

**Forms/CommonForms/**
- Basic shared forms available to all users
- Application infrastructure forms
- Examples: LoginForm, MainFrame, AboutBox

**Forms/ReusableForms/**
- Forms and user controls that can be called by any other form
- Utility and shared UI components
- Examples: MenuRibbon (UC), ParamEntryForm, PrintPreviewForm


### Modules Folder Placement Rules

**Modules/Data/[FormName]/**
- Repository classes for the specific form (e.g., `EstimateForm-Repository.cs`)
- Query service classes for the form (e.g., `EstimateForm-QueryService.cs`)
- Form-specific data access utilities

**Modules/Models/[FormName]/**
- Entity classes representing database tables used by the form
- Data transfer objects specific to the form
- Form-specific model classes (e.g., `EstimateForm-HeaderModel.cs`)

**Modules/Helpers/[FormName]/**
- Form-specific UI helper classes (e.g., `EstimateForm-Helper.cs`)
- Form-specific validation logic (e.g., `EstimateForm-Validation.cs`)
- Form-specific utility functions

**Modules/Helpers/ (Root Level - Shared Only)**
- Utility classes used across multiple forms/modules
- Configuration management (`ConfigurationHelper.cs`)
- File I/O utilities (`SQLQueryLoader.cs`)

**Modules/UI/**
- UI-specific services (progress indicators, notifications)
- Form state management utilities
- UI helper classes

**Modules/Connections/**
- Database connection management
- Transaction handling
- Query execution utilities

**Modules/Procedures/**
- All SQL query files
- Organized by functional module
- Named queries within files using comments

## File Splitting Guidelines

### When to Split Large Files

Files should be split when they exceed **1000 lines** (preferred limit: **500 lines**) to maintain readability and performance. The splitting strategy depends on the file type:

### Form File Splitting Strategy

**Forms Folder Rules:**
- Only 3 files per form: `.cs`, `.designer.cs`, and `.resx`
- All supporting logic goes to `modules/` folder
- Main form file should contain only essential UI logic

**Example: EstimateForm Splitting**
```
Forms/
├── EstimateForm.cs (≤800 lines)           # Main form with essential UI logic
├── EstimateForm.designer.cs               # Auto-generated designer file
└── EstimateForm.resx                      # Auto-generated resources

modules/helpers/
├── EstimateForm-Helper.cs (≤900 lines)    # UI helpers, grid, navigation, form state
└── EstimateForm-Validation.cs (≤600 lines) # Data validation and business rules

modules/Data/
├── EstimateForm-Repository.cs (≤900 lines) # Core CRUD + Search operations
└── EstimateForm-QueryService.cs (≤700 lines) # SQL operations and data mapping
```

### Repository File Splitting Strategy

**Large Repository Files (>1000 lines):**
- Combine CRUD operations with search functionality in main repository
- Extract complex mapping logic to separate helper methods
- Keep navigation queries in the same file for cohesion
- Split only when file exceeds 1000 lines significantly

**Example: Repository Organization**
```csharp
// EstimateForm-Repository.cs (≤900 lines)
public static class EstimateFormRepository
{
    #region Core CRUD Operations
    // GetEstimateById, SaveEstimate, DeleteEstimate

    #region Search Operations
    // GetAllEstimates, SearchEstimatesByCustomer, GetEstimateByNumber

    #region Navigation Operations
    // GetFirstEstimate, GetLastEstimate, GetNextEstimate, GetPreviousEstimate

    #region Utility Methods
    // GetNextEstimateNumber, GetEstimateDetailsById, GetEstimateCount

    #region Private Helper Methods
    // MapEstimateFromReader, InsertEstimateHeader, UpdateEstimateHeader
}
```

### Helper File Organization

**Form-Specific Helpers:**
- `[FormName]-Helper.cs` - UI operations, grid management, navigation, form state
- `[FormName]-Validation.cs` - Data validation, business rules, error handling

**Shared Helpers:**
- Place in `modules/helpers/` without form prefix
- Examples: `ConfigurationHelper.cs`, `SQLQueryLoader.cs`

### File Size Targets

| File Type | Preferred Limit | Maximum Limit | Action Required |
|-----------|----------------|---------------|-----------------|
| **Form Files** | 500 lines | 800 lines | Split to helpers |
| **Repository Files** | 500 lines | 900 lines | Combine related operations |
| **Helper Files** | 500 lines | 900 lines | Split by functionality |
| **Model Files** | 200 lines | 300 lines | Split by entity |
| **Service Files** | 500 lines | 700 lines | Split by responsibility |

## Progress Indicator System

### Overview
ProManage implements a centralized progress indicator system that provides consistent user feedback **specifically during database operations** across all forms. The system is designed to keep form code minimal while ensuring professional user experience when the application is communicating with the database.

### Architecture Components

**Core Service:**
- `Modules/UI/ProgressIndicatorService.cs` - Singleton service managing the MainFrame progress bar

**MainFrame Integration:**
- `Forms/MainFrame.cs` - Initializes the service with statusProgressBar control
- `Forms/MainFrame.Designer.cs` - Contains MarqueeProgressBarControl definition

**Key Features:**
- **Centralized Control**: Single service manages progress indication across all forms
- **Thread Safety**: Safe for use in multi-threaded operations
- **Nested Operations**: Supports multiple concurrent operations through reference counting
- **Minimum Display Time**: Ensures 500ms minimum visibility for better UX
- **Automatic Cleanup**: Handles errors and ensures proper cleanup

### Usage Pattern in Forms

**Standard Implementation (2 lines of code):**
```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Database operation (CRUD, search, etc.)
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

**When to Use (Database Operations Only):**
- Database CRUD operations (Create, Read, Update, Delete)
- Database search and query operations
- Report generation from database
- Form initialization that loads data from database
- Navigation operations that query database

**Integration Benefits:**
- **Minimal Form Code**: Only 2 lines needed per database operation
- **Consistent UX**: Same progress indication across all forms during database communication
- **Error Resilient**: Automatic cleanup even if database operations fail
- **Professional Appearance**: Smooth, responsive user feedback during database operations

> **📖 For Complete Details**: See [ProgressBarUsageGuide.md](ProgressBarUsageGuide.md) for comprehensive implementation examples, best practices, and troubleshooting guidelines.

## Best Practices for File Organization

1. **Group related functionality** in form-specific subfolders
2. **Use descriptive folder names** that clearly indicate purpose (FormName pattern)
3. **Maintain consistent depth** - avoid deeply nested folder structures
4. **Separate concerns** - keep UI, data, and business logic in appropriate folders
5. **Document relationships** between files and folders
6. **Follow naming conventions** consistently across all folders
7. **Prefer 1-2 files per important process** rather than many small files
8. **Keep files under 1000 lines** (500 preferred) for maintainability
9. **Use partial classes** for form splitting to maintain functionality
10. **Extract complex logic** to helper classes rather than inline code
11. **Use centralized progress indication** for all database operations
12. **CRITICAL: Models must contain ONLY data properties** - no business logic or validation methods
13. **Move validation logic** to `[FormName]-Validation.cs` helper files
14. **Move business logic** to `[FormName]-Helper.cs` or service files

## Build Output Organization

The build process automatically copies SQL files to the output directory maintaining the same folder structure:

```
bin/Debug/
├── Modules/
│   └── Procedures/
│       ├── Estimate/
│       ├── SQLQuery/
│       ├── System/
│       └── User/
├── ProManage.exe
├── ProManage.exe.config
└── [Other assemblies and dependencies]
```

This ensures SQL queries are available at runtime for the SQLQueryLoader utility.
