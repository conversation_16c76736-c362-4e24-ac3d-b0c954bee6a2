using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Enhanced thread-safe cache for permission results with performance monitoring.
    /// Implements automatic expiration, selective cache invalidation, user permission sets,
    /// and advanced performance optimization features.
    /// </summary>
    public class PermissionCache : IDisposable
    {
        private readonly ConcurrentDictionary<string, CacheItem> _cache = new ConcurrentDictionary<string, CacheItem>();
        private readonly ConcurrentDictionary<int, UserPermissionSet> _userPermissionSets = new ConcurrentDictionary<int, UserPermissionSet>();
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(15);
        private readonly Timer _cleanupTimer;
        private readonly PermissionPerformanceMonitor _performanceMonitor;

        public PermissionCache()
        {
            _performanceMonitor = new PermissionPerformanceMonitor();

            // Setup cleanup timer to run every 5 minutes
            _cleanupTimer = new Timer(CleanupExpiredEntries, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        #region Enhanced Cache Operations

        /// <summary>
        /// Try to get permission from cache with performance monitoring
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="permission">Output permission value</param>
        /// <returns>True if found in cache and not expired, false otherwise</returns>
        public bool TryGetPermission(string key, out bool permission)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            permission = false;

            try
            {
                if (_cache.TryGetValue(key, out CacheItem item))
                {
                    if (DateTime.Now - item.Timestamp < _cacheExpiry)
                    {
                        permission = item.Value;
                        item.LastAccess = DateTime.Now;
                        item.AccessCount++;
                        _performanceMonitor.RecordCacheHit(stopwatch.ElapsedMilliseconds);
                        return true;
                    }
                    else
                    {
                        // Remove expired item
                        _cache.TryRemove(key, out _);
                        _performanceMonitor.RecordCacheExpiry();
                    }
                }

                _performanceMonitor.RecordCacheMiss(stopwatch.ElapsedMilliseconds);
                return false;
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        /// <summary>
        /// Set permission in cache with metadata
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="permission">Permission value to cache</param>
        /// <param name="source">Source of the permission</param>
        public void SetPermission(string key, bool permission, PermissionSource source = PermissionSource.Role)
        {
            _cache[key] = new CacheItem
            {
                Value = permission,
                Timestamp = DateTime.Now,
                LastAccess = DateTime.Now,
                Source = source,
                AccessCount = 1
            };

            _performanceMonitor.RecordCacheSet();
        }

        /// <summary>
        /// Preload user permission set for faster access
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="permissions">Dictionary of permissions</param>
        public void PreloadUserPermissions(int userId, Dictionary<string, bool> permissions)
        {
            var permissionSet = new UserPermissionSet
            {
                UserId = userId,
                Permissions = new ConcurrentDictionary<string, bool>(permissions),
                LoadTime = DateTime.Now,
                LastAccess = DateTime.Now
            };

            _userPermissionSets[userId] = permissionSet;
            _performanceMonitor.RecordUserPreload(userId);
        }

        /// <summary>
        /// Try to get permission from preloaded user set
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissionType">Permission type</param>
        /// <param name="permission">Output permission value</param>
        /// <returns>True if found in user set</returns>
        public bool TryGetUserPermission(int userId, string formName, PermissionType permissionType, out bool permission)
        {
            permission = false;

            if (_userPermissionSets.TryGetValue(userId, out UserPermissionSet userSet))
            {
                if (DateTime.Now - userSet.LoadTime < _cacheExpiry)
                {
                    var key = $"{formName}_{permissionType}";
                    if (userSet.Permissions.TryGetValue(key, out permission))
                    {
                        userSet.LastAccess = DateTime.Now;
                        _performanceMonitor.RecordUserSetHit(userId);
                        return true;
                    }
                }
                else
                {
                    // Remove expired user set
                    _userPermissionSets.TryRemove(userId, out _);
                }
            }

            return false;
        }

        #endregion

        #region Cache Management

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        public void ClearAll()
        {
            _cache.Clear();
            _userPermissionSets.Clear();
        }

        /// <summary>
        /// Clear cache for specific user with performance tracking
        /// </summary>
        /// <param name="userId">User ID to clear cache for</param>
        public void ClearUserPermissions(int userId)
        {
            var keysToRemove = _cache.Keys.Where(k => k.StartsWith($"{userId}_")).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }

            _userPermissionSets.TryRemove(userId, out _);
            _performanceMonitor.RecordUserCacheClear(userId);
        }

        /// <summary>
        /// Clear cache for specific role (affects all users with that role)
        /// </summary>
        /// <param name="roleId">Role ID to clear cache for</param>
        public void ClearRolePermissions(int roleId)
        {
            // For role changes, clear all cache since we don't track role-user mapping in cache
            var clearedCount = _cache.Count;
            ClearAll();
            _performanceMonitor.RecordRoleCacheClear(roleId, clearedCount);
        }

        /// <summary>
        /// Clear global permissions for user
        /// </summary>
        /// <param name="userId">User ID to clear global permissions for</param>
        public void ClearGlobalPermissions(int userId)
        {
            var keysToRemove = _cache.Keys.Where(k => k.StartsWith($"global_{userId}_")).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// Intelligent cache warming for frequently accessed permissions
        /// </summary>
        /// <param name="userIds">List of user IDs to warm</param>
        /// <param name="formNames">List of form names to warm</param>
        public async Task WarmCache(List<int> userIds, List<string> formNames)
        {
            await Task.Run(() =>
            {
                var preloader = new PermissionPreloader();

                foreach (var userId in userIds)
                {
                    var permissions = preloader.LoadUserPermissions(userId, formNames);
                    PreloadUserPermissions(userId, permissions);
                }

                _performanceMonitor.RecordCacheWarm(userIds.Count, formNames.Count);
            });
        }

        #endregion

        #region Performance Monitoring

        /// <summary>
        /// Get cache performance statistics
        /// </summary>
        /// <returns>Enhanced cache statistics</returns>
        public CacheStatistics GetStatistics()
        {
            return new CacheStatistics
            {
                TotalEntries = _cache.Count,
                UserSets = _userPermissionSets.Count,
                HitRate = _performanceMonitor.GetHitRate(),
                AverageAccessTime = _performanceMonitor.GetAverageAccessTime(),
                ExpiredEntries = (int)_performanceMonitor.ExpiredEntries,
                LastCleanup = _performanceMonitor.LastCleanup
            };
        }

        /// <summary>
        /// Get most accessed permissions for optimization
        /// </summary>
        /// <param name="topCount">Number of top permissions to return</param>
        /// <returns>List of most accessed permission keys</returns>
        public List<string> GetMostAccessedPermissions(int topCount = 10)
        {
            return _cache
                .OrderByDescending(kvp => kvp.Value.AccessCount)
                .Take(topCount)
                .Select(kvp => kvp.Key)
                .ToList();
        }

        #endregion

        #region Cleanup and Maintenance

        /// <summary>
        /// Clean expired cache entries (called by timer)
        /// </summary>
        /// <param name="state">Timer state (unused)</param>
        private void CleanupExpiredEntries(object state)
        {
            var expiredKeys = _cache
                .Where(kvp => DateTime.Now - kvp.Value.Timestamp >= _cacheExpiry)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }

            // Clean expired user permission sets
            var expiredUserSets = _userPermissionSets
                .Where(kvp => DateTime.Now - kvp.Value.LoadTime >= _cacheExpiry)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var userId in expiredUserSets)
            {
                _userPermissionSets.TryRemove(userId, out _);
            }

            _performanceMonitor.RecordCleanup(expiredKeys.Count + expiredUserSets.Count);
        }

        /// <summary>
        /// Force cleanup of all expired entries
        /// </summary>
        public void CleanExpiredEntries()
        {
            CleanupExpiredEntries(null);
        }

        /// <summary>
        /// Optimize cache by removing least recently used items when memory pressure is high
        /// </summary>
        /// <param name="maxEntries">Maximum number of entries to keep</param>
        public void OptimizeCache(int maxEntries = 1000)
        {
            if (_cache.Count > maxEntries)
            {
                var itemsToRemove = _cache
                    .OrderBy(kvp => kvp.Value.LastAccess)
                    .Take(_cache.Count - maxEntries)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in itemsToRemove)
                {
                    _cache.TryRemove(key, out _);
                }

                _performanceMonitor.RecordOptimization(itemsToRemove.Count);
            }
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }

        #endregion

        #region Cache Item Classes

        /// <summary>
        /// Enhanced cache item structure with metadata
        /// </summary>
        private class CacheItem
        {
            public bool Value { get; set; }
            public DateTime Timestamp { get; set; }
            public DateTime LastAccess { get; set; } = DateTime.Now;
            public PermissionSource Source { get; set; }
            public int AccessCount { get; set; }
        }

        /// <summary>
        /// User permission set for bulk access
        /// </summary>
        private class UserPermissionSet
        {
            public int UserId { get; set; }
            public ConcurrentDictionary<string, bool> Permissions { get; set; }
            public DateTime LoadTime { get; set; }
            public DateTime LastAccess { get; set; }
        }

        #endregion
    }

    /// <summary>
    /// Enhanced cache statistics information
    /// </summary>
    public class CacheStatistics
    {
        public int TotalEntries { get; set; }
        public int UserSets { get; set; }
        public double HitRate { get; set; }
        public double AverageAccessTime { get; set; }
        public int ExpiredEntries { get; set; }
        public DateTime LastCleanup { get; set; }

        public override string ToString()
        {
            return $"Cache: {TotalEntries} entries, {UserSets} user sets, {HitRate:F1}% hit rate, {AverageAccessTime:F2}ms avg access";
        }
    }
}
